<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.innodealing.onshore.bondservice.mapper.bond.OnshoreBondFilterMapper">

    <select id="listCrossMarketDedupStatusBondUniCodes" resultType="java.lang.Long">
        SELECT bond_uni_code
        FROM (SELECT bond_uni_code, ROW_NUMBER() OVER (PARTITION BY bond_id ORDER BY
        CASE second_market
        WHEN 3 THEN 1
        WHEN 2 THEN 2
        WHEN 1 THEN 3
        WHEN 78 THEN 4
        ELSE 999
        END) as rn
        FROM onshore_bond_filter WHERE 1=1
        <if test="comUniCodes != null and comUniCodes.size() > 0">
            and com_uni_code IN
            <foreach item="item" collection="comUniCodes" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="bondUniCodes != null and bondUniCodes.size() > 0">
            and bond_uni_code IN
            <foreach item="item" collection="bondUniCodes" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="secondMarkets != null and secondMarkets.size() > 0">
            and second_market IN
            <foreach item="item" collection="secondMarkets" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>
        ) ranked
        WHERE rn = 1;
    </select>


    <select id="selectOnshoreBondFilterStatistics" resultType="com.innodealing.onshore.bondservice.model.entity.bond.view.OnshoreBondFilterView"
            parameterType="com.innodealing.onshore.bondservice.model.bo.BondBalanceStatisticsBO">
        select
            <if test="crossMarketDedupStatus == null or crossMarketDedupStatus == 0">
                sum(no_market_outstanding_status_bond_count) duration_count,
                sum(no_market_expired_bond_count) expired_count,
            </if>
            <if test="crossMarketDedupStatus == 1">
                COUNT(CASE WHEN outstanding_status = 1 THEN 1 END) duration_count,
                COUNT(CASE WHEN expired = 1 THEN 1 END) expired_count,
            </if>
            SUM(CASE WHEN outstanding_status = 1 THEN bond_balance ELSE 0 END) duration_total_bond_balance,
            SUM(CASE WHEN expired = 1 THEN bond_balance ELSE 0 END) expired_total_bond_balance
        from (
             <include refid="selectOnshoreBondFilterStatistics"/>
             ) as temp;
    </select>


    <sql  id="selectOnshoreBondFilterStatistics" >
        SELECT
        count(CASE WHEN outstanding_status = 1 THEN 1 END) as no_market_outstanding_status_bond_count,
        count(CASE WHEN expired = 1 THEN 1 END) as no_market_expired_bond_count,
        outstanding_status,
        bond_balance,
        expired
        FROM onshore_bond_filter
        WHERE deleted = 0
        <!-- 过滤发行状态：排除延迟发行(2)、取消发行(3)、其他(999) -->
        AND issue_status NOT IN (2, 3, 999)

        <!-- 主体唯一编码过滤 -->
        <if test="comUniCodes != null and comUniCodes.size() > 0">
            AND com_uni_code IN
            <foreach collection="comUniCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>

        <!-- 债券唯一编码过滤 -->
        <if test="bondUniCodes != null and bondUniCodes.size() > 0">
            AND bond_uni_code IN
            <foreach collection="bondUniCodes" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>

        <!-- 债券类型过滤 -->
        <if test="bondTypes != null and bondTypes.size() > 0">
            AND bond_type IN
            <foreach collection="bondTypes" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>

        <if test="secondMarkets != null and secondMarkets.size > 0">
            AND second_market IN
            <foreach collection="secondMarkets" item="code" open="(" separator="," close=")">
                #{code}
            </foreach>
        </if>

        <!-- 募集方式过滤 -->
        <if test="publicOfferings != null and publicOfferings.size() > 0">
            AND public_offering IN
            <foreach collection="publicOfferings" item="offering" open="(" separator="," close=")">
                #{offering}
            </foreach>
        </if>

        <!-- 债券状态过滤 -->
        <if test="bondStatus != null and bondStatus.size() > 0">
            AND (
            <trim prefixOverrides="OR">
                <!-- 存续债券 -->
                <if test="bondStatus.contains(17)">
                    OR outstanding_status = 1
                </if>
                <!-- 到期债券 -->
                <if test="bondStatus.contains(18)">
                    OR expired = 1
                </if>
                <!-- 发行中债券 -->
                <if test="bondStatus.contains(0)">
                    OR issue_status = 0
                </if>
            </trim>
            )
        </if>

        <!-- 债券条款过滤 -->
        <if test="bondTerms != null and bondTerms.size() > 0">
            AND (
            <trim prefixOverrides="OR">
                <!-- 永续债券 -->
                <if test="bondTerms.contains(108)">
                    OR embedded_option = 2
                </if>
                <!-- 回售债券 -->
                <if test="bondTerms.contains(106)">
                    OR put_option_status = 1
                </if>
                <!-- 赎回债券 -->
                <if test="bondTerms.contains(105)">
                    OR redeem_status = 1
                </if>
                <!-- 票面利率选择权 -->
                <if test="bondTerms.contains(12)">
                    OR coupon_adjustable_status = 1
                </if>
            </trim>
            )
        </if>
        GROUP BY bond_id
    </sql>

    <!-- 优化版本的分页查询 - 使用INNER JOIN替代大量IN查询 -->
    <select id="selectPageOnshoreBondFilterPageOptimized"
            resultType="com.innodealing.onshore.bondservice.model.entity.bond.OnshoreBondFilterDO"
            parameterType="com.innodealing.onshore.bondservice.model.dto.request.BondDetailInfoPageRequestDTO">
        SELECT main.*
        FROM onshore_bond_filter main
        <include refid="optimizedCrossMarketJoin"/>
        WHERE 1=1
        <include refid="commonWhereConditions"/>
        <include refid="dynamicOrderBy"/>
        <if test="pageNum != null and pageSize != null">
            LIMIT ${offset} , ${pageSize}
        </if>
    </select>


    <select id="selectCountOnshoreBondFilterPageOptimized"
            resultType="java.lang.Integer"
            parameterType="com.innodealing.onshore.bondservice.model.dto.request.BondDetailInfoPageRequestDTO">
        SELECT count(*)
        FROM onshore_bond_filter main
        <include refid="optimizedCrossMarketJoin"/>
        WHERE 1=1
        <include refid="commonWhereConditions"/>
    </select>

    <!-- 优化的跨市场去重JOIN子查询 -->
    <sql id="optimizedCrossMarketJoin">
        <choose>
            <!-- 情况1: 跨市场并且有勾选市场筛选 且这是ABS债券 -->
            <when test="absBondUniCodes != null and absBondUniCodes.size() > 0
                       and secondMarkets != null and secondMarkets.size() > 0
                       and crossMarketDedupStatus != null and crossMarketDedupStatus == 1">
                INNER JOIN (
                    SELECT bond_uni_code
                    FROM (
                        SELECT bond_uni_code,
                               ROW_NUMBER() OVER (PARTITION BY bond_id ORDER BY
                                   CASE second_market
                                       WHEN 3 THEN 1
                                       WHEN 2 THEN 2
                                       WHEN 1 THEN 3
                                       WHEN 78 THEN 4
                                       ELSE 999
                                   END) as rn
                        FROM onshore_bond_filter
                        WHERE 1=1
                        AND main.deleted = 0
                        AND main.issue_status NOT IN (2, 3, 999)
                        AND bond_uni_code IN
                        <foreach item="item" collection="absBondUniCodes" index="index" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                        AND second_market IN
                        <foreach item="item" collection="secondMarkets" index="index" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    ) ranked
                    WHERE rn = 1
                ) dedup_abs ON main.bond_uni_code = dedup_abs.bond_uni_code
            </when>
            <!-- 情况2: 跨市场并且有勾选市场筛选 且这是本主体或者实控人 -->
            <when test="(relationComUniCodes != null and relationComUniCodes.size() > 0 or comUniCode != null)
                       and secondMarkets != null and secondMarkets.size() > 0
                       and crossMarketDedupStatus != null and crossMarketDedupStatus == 1">
                INNER JOIN (
                    SELECT bond_uni_code
                    FROM (
                        SELECT bond_uni_code,
                               ROW_NUMBER() OVER (PARTITION BY bond_id ORDER BY
                                   CASE second_market
                                       WHEN 3 THEN 1
                                       WHEN 2 THEN 2
                                       WHEN 1 THEN 3
                                       WHEN 78 THEN 4
                                       ELSE 999
                                   END) as rn
                        FROM onshore_bond_filter
                        WHERE 1=1
                        AND main.deleted = 0
                        AND main.issue_status NOT IN (2, 3, 999)
                        <if test="comUniCode != null or (relationComUniCodes != null and relationComUniCodes.size() > 0)">
                            AND com_uni_code IN (
                            <if test="comUniCode != null">
                                #{comUniCode}
                                <if test="relationComUniCodes != null and relationComUniCodes.size() > 0">,</if>
                            </if>
                            <if test="relationComUniCodes != null and relationComUniCodes.size() > 0">
                                <foreach item="item" collection="relationComUniCodes" index="index" separator=",">
                                    #{item}
                                </foreach>
                            </if>
                            )
                        </if>
                        AND second_market IN
                        <foreach item="item" collection="secondMarkets" index="index" separator="," open="(" close=")">
                            #{item}
                        </foreach>
                    ) ranked
                    WHERE rn = 1
                ) dedup_com ON main.bond_uni_code = dedup_com.bond_uni_code
            </when>
            <!-- 情况3: 其他情况不需要JOIN -->
            <otherwise>
                <!-- 不需要额外的JOIN -->
            </otherwise>
        </choose>
    </sql>

    <!-- 通用WHERE条件 -->
    <sql id="commonWhereConditions">
        <!-- 基础过滤条件 -->
        AND main.deleted = 0
        AND main.issue_status NOT IN (2, 3, 999)

        <!-- 主体编码过滤 -->
        <if test="comUniCode != null">
            AND main.com_uni_code = #{comUniCode}
        </if>

        <!-- 关联主体编码过滤 -->
        <if test="relationComUniCodes != null and relationComUniCodes.size() > 0">
            AND main.com_uni_code IN
            <foreach item="item" collection="relationComUniCodes" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <!-- ABS债券编码过滤 -->
        <if test="absBondUniCodes != null and absBondUniCodes.size() > 0">
            AND main.bond_uni_code IN
            <foreach item="item" collection="absBondUniCodes" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <!-- 二级市场过滤 -->
        <if test="secondMarkets != null and secondMarkets.size() > 0">
            AND main.second_market IN
            <foreach item="item" collection="secondMarkets" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <!-- 募集方式过滤 -->
        <if test="publicOfferings != null and publicOfferings.size() > 0">
            AND main.public_offering IN
            <foreach item="item" collection="publicOfferings" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <!-- 债券类型过滤 -->
        <if test="bondTypes != null and bondTypes.size() > 0">
            AND main.bond_type IN
            <foreach item="item" collection="bondTypes" index="index" separator="," open="(" close=")">
                #{item}
            </foreach>
        </if>

        <!-- 债券状态过滤 -->
        <if test="bondStatus != null and bondStatus.size() > 0">
            AND (
            <trim prefixOverrides="OR">
                <!-- 存续债券 (17) -->
                <if test="bondStatus.contains(17)">
                    OR main.outstanding_status = 1
                </if>
                <!-- 到期债券 (18) -->
                <if test="bondStatus.contains(18)">
                    OR main.expired = 1
                </if>
                <!-- 发行中债券 (0) -->
                <if test="bondStatus.contains(0)">
                    OR main.issue_status = 0
                </if>
            </trim>
            )
        </if>

        <!-- 债券条款过滤 -->
        <if test="bondTerms != null and bondTerms.size() > 0">
            AND (
            <trim prefixOverrides="OR">
                <!-- 永续债券 (108) -->
                <if test="bondTerms.contains(108)">
                    OR main.embedded_option = 2
                </if>
                <!-- 回售债券 (106) -->
                <if test="bondTerms.contains(106)">
                    OR main.put_option_status = 1
                </if>
                <!-- 赎回债券 (105) -->
                <if test="bondTerms.contains(105)">
                    OR main.redeem_status = 1
                </if>
                <!-- 票面利率选择权 (12) -->
                <if test="bondTerms.contains(12)">
                    OR main.coupon_adjustable_status = 1
                </if>
            </trim>
            )
        </if>


        <!-- 跨市场去重状态过滤 (当没有勾选市场筛选时) -->
        <if test="(secondMarkets == null or secondMarkets.size() == 0) and crossMarketDedupStatus != null and crossMarketDedupStatus == 1">
            AND main.cross_market_dedup_status = 1
        </if>
    </sql>

    <!-- 动态排序 -->
    <sql id="dynamicOrderBy">
        ORDER BY
            main.expired,
            <choose>
                <!-- 如果有特定的排序字段 -->
                <when test="sortProperty != null and sortProperty != ''">
                    <choose>
                        <!-- 特殊收益率字段处理 -->
                        <when test="sortProperty == 'cb_yte' or sortProperty == 'cs_yte' or sortProperty == 'cb_ytm' or sortProperty == 'cs_ytm'">
                            <choose>
                                <when test="sortProperty == 'cb_yte' or sortProperty == 'cb_ytm'">
                                    CASE WHEN main.cb_yte IS NOT NULL THEN main.cb_yte ELSE main.cb_ytm END
                                </when>
                                <when test="sortProperty == 'cs_yte' or sortProperty == 'cs_ytm'">
                                    CASE WHEN main.cs_yte IS NOT NULL THEN main.cs_yte ELSE main.cs_ytm END
                                </when>
                            </choose>
                            <choose>
                                <when test="sortDirection != null and sortDirection.name() == 'DESC'">DESC</when>
                                <otherwise>ASC</otherwise>
                            </choose>,
                        </when>
                        <!-- 普通字段处理 -->
                        <otherwise>
                            <!-- 将驼峰命名转换为下划线命名 -->
                            <!-- 这里简化处理，实际应该有完整的字段映射 -->
                            ${sortProperty}
                            <choose>
                                <when test="sortDirection != null and sortDirection.name() == 'DESC'">DESC</when>
                                <otherwise>ASC</otherwise>
                            </choose>,
                        </otherwise>
                    </choose>
                </when>
            </choose>
            CASE
                WHEN main.expired = 0 THEN ISNULL(main.list_date)
                WHEN main.expired = 1 THEN ISNULL(main.maturity_date)
                ELSE 0
            END,
            CASE
                WHEN main.expired = 0 THEN main.list_date
                WHEN main.expired = 1 THEN main.maturity_date
            END DESC,
            FIELD(main.second_market, 3, 2, 1, 78, 99)
    </sql>
</mapper>