package com.innodealing.onshore.bondservice.dao.bond;

import com.github.wz2cool.dynamic.SortDirection;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.onshore.bondservice.model.dto.request.BondDetailInfoPageRequestDTO;
import com.innodealing.onshore.bondservice.model.entity.bond.OnshoreBondFilterDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * OnshoreBondFilterDAO 优化版本测试类
 * 对比原版本和优化版本的性能和结果一致性
 * 
 * <AUTHOR>
 */
@SpringBootTest
@ActiveProfiles("test")
public class OnshoreBondFilterDAOOptimizedTest {

    @Resource
    private OnshoreBondFilterDAO onshoreBondFilterDAO;

    private BondDetailInfoPageRequestDTO testRequest;

    @BeforeEach
    void setUp() {
        // 准备测试数据
        testRequest = new BondDetailInfoPageRequestDTO();
        testRequest.setComUniCode(10007984L);
        testRequest.setSecondMarkets(Arrays.asList(3, 2, 1));
        testRequest.setCrossMarketDedupStatus(1);
        testRequest.setPublicOfferings(Collections.singletonList(0));
        testRequest.setPageNum(0);
        testRequest.setPageSize(50);
        testRequest.setSortProperty("listDate");
        testRequest.setSortDirection(SortDirection.DESC);
    }

    @Test
    void testOptimizedMethodExists() {
        // 测试优化方法是否存在
        assertNotNull(onshoreBondFilterDAO);
        
        // 这里只是验证方法可以调用，实际测试需要数据库环境
        try {
            NormPagingResult<OnshoreBondFilterDO> result = 
                onshoreBondFilterDAO.getOnshoreBondFilterPageOptimized(testRequest);
            assertNotNull(result);
        } catch (Exception e) {
            // 在没有数据库连接的情况下，这是预期的
            System.out.println("Expected exception in test environment: " + e.getMessage());
        }
    }

    @Test
    void testPerformanceComparison() {
        System.out.println("=== 性能对比测试 ===");
        
        // 测试原版本性能
        long startTime = System.currentTimeMillis();
        try {
            NormPagingResult<OnshoreBondFilterDO> originalResult = 
                onshoreBondFilterDAO.getOnshoreBondFilterPage(testRequest);
            long originalTime = System.currentTimeMillis() - startTime;
            System.out.println("原版本执行时间: " + originalTime + "ms");
            System.out.println("原版本结果数量: " + (originalResult != null ? originalResult.getList().size() : 0));
        } catch (Exception e) {
            System.out.println("原版本执行异常: " + e.getMessage());
        }

        // 测试优化版本性能
        startTime = System.currentTimeMillis();
        try {
            NormPagingResult<OnshoreBondFilterDO> optimizedResult = 
                onshoreBondFilterDAO.getOnshoreBondFilterPageOptimized(testRequest);
            long optimizedTime = System.currentTimeMillis() - startTime;
            System.out.println("优化版本执行时间: " + optimizedTime + "ms");
            System.out.println("优化版本结果数量: " + (optimizedResult != null ? optimizedResult.getList().size() : 0));
        } catch (Exception e) {
            System.out.println("优化版本执行异常: " + e.getMessage());
        }
    }

    @Test
    void testResultConsistency() {
        System.out.println("=== 结果一致性测试 ===");
        
        try {
            // 获取原版本结果
            NormPagingResult<OnshoreBondFilterDO> originalResult = 
                onshoreBondFilterDAO.getOnshoreBondFilterPage(testRequest);
            
            // 获取优化版本结果
            NormPagingResult<OnshoreBondFilterDO> optimizedResult = 
                onshoreBondFilterDAO.getOnshoreBondFilterPageOptimized(testRequest);
            
            if (originalResult != null && optimizedResult != null) {
                // 验证总数一致
                assertEquals(originalResult.getTotal(), optimizedResult.getTotal(), 
                    "总记录数应该一致");
                
                // 验证当前页数据数量一致
                assertEquals(originalResult.getList().size(), optimizedResult.getList().size(), 
                    "当前页记录数应该一致");
                
                // 验证第一条记录的bondUniCode一致（验证排序一致性）
                if (!originalResult.getList().isEmpty() && !optimizedResult.getList().isEmpty()) {
                    assertEquals(
                        originalResult.getList().get(0).getBondUniCode(),
                        optimizedResult.getList().get(0).getBondUniCode(),
                        "第一条记录的bondUniCode应该一致"
                    );
                }
                
                System.out.println("结果一致性验证通过");
                System.out.println("原版本总数: " + originalResult.getTotal());
                System.out.println("优化版本总数: " + optimizedResult.getTotal());
            }
            
        } catch (Exception e) {
            System.out.println("结果一致性测试异常: " + e.getMessage());
        }
    }

    @Test
    void testDifferentScenarios() {
        System.out.println("=== 不同场景测试 ===");
        
        // 场景1: 大量bondUniCodes的情况
        BondDetailInfoPageRequestDTO largeRequest = new BondDetailInfoPageRequestDTO();
        largeRequest.setComUniCode(10007984L);
        largeRequest.setSecondMarkets(Arrays.asList(3, 2, 1));
        largeRequest.setCrossMarketDedupStatus(1);
        largeRequest.setPageNum(0);
        largeRequest.setPageSize(100);
        
        testScenario("大量数据场景", largeRequest);
        
        // 场景2: 只有主体编码的情况
        BondDetailInfoPageRequestDTO simpleRequest = new BondDetailInfoPageRequestDTO();
        simpleRequest.setComUniCode(10007984L);
        simpleRequest.setPageNum(0);
        simpleRequest.setPageSize(20);
        
        testScenario("简单查询场景", simpleRequest);
        
        // 场景3: 包含关联主体的情况
        BondDetailInfoPageRequestDTO relationRequest = new BondDetailInfoPageRequestDTO();
        relationRequest.setComUniCode(10007984L);
        relationRequest.setRelationComUniCodes(Arrays.asList(10007985L, 10007986L));
        relationRequest.setSecondMarkets(Arrays.asList(3, 2, 1));
        relationRequest.setCrossMarketDedupStatus(1);
        relationRequest.setPageNum(0);
        relationRequest.setPageSize(30);
        
        testScenario("关联主体场景", relationRequest);
    }

    private void testScenario(String scenarioName, BondDetailInfoPageRequestDTO request) {
        System.out.println("\n--- " + scenarioName + " ---");
        
        try {
            // 测试原版本
            long startTime = System.currentTimeMillis();
            NormPagingResult<OnshoreBondFilterDO> originalResult = 
                onshoreBondFilterDAO.getOnshoreBondFilterPage(request);
            long originalTime = System.currentTimeMillis() - startTime;
            
            // 测试优化版本
            startTime = System.currentTimeMillis();
            NormPagingResult<OnshoreBondFilterDO> optimizedResult = 
                onshoreBondFilterDAO.getOnshoreBondFilterPageOptimized(request);
            long optimizedTime = System.currentTimeMillis() - startTime;
            
            System.out.println("原版本: " + originalTime + "ms, 结果数: " + 
                (originalResult != null ? originalResult.getList().size() : 0));
            System.out.println("优化版本: " + optimizedTime + "ms, 结果数: " + 
                (optimizedResult != null ? optimizedResult.getList().size() : 0));
            
            if (originalTime > 0 && optimizedTime > 0) {
                double improvement = ((double)(originalTime - optimizedTime) / originalTime) * 100;
                System.out.println("性能提升: " + String.format("%.2f", improvement) + "%");
            }
            
        } catch (Exception e) {
            System.out.println("场景测试异常: " + e.getMessage());
        }
    }

    @Test
    void testEdgeCases() {
        System.out.println("=== 边界情况测试 ===");
        
        // 测试空参数
        BondDetailInfoPageRequestDTO emptyRequest = new BondDetailInfoPageRequestDTO();
        testEdgeCase("空参数", emptyRequest);
        
        // 测试null值
        testEdgeCase("null参数", null);
        
        // 测试大分页
        BondDetailInfoPageRequestDTO largePagingRequest = new BondDetailInfoPageRequestDTO();
        largePagingRequest.setComUniCode(10007984L);
        largePagingRequest.setPageNum(0);
        largePagingRequest.setPageSize(1000);
        testEdgeCase("大分页", largePagingRequest);
    }

    private void testEdgeCase(String caseName, BondDetailInfoPageRequestDTO request) {
        System.out.println("\n--- " + caseName + " ---");
        
        try {
            NormPagingResult<OnshoreBondFilterDO> result = 
                onshoreBondFilterDAO.getOnshoreBondFilterPageOptimized(request);
            System.out.println("结果: " + (result != null ? "成功" : "null"));
            if (result != null) {
                System.out.println("记录数: " + result.getList().size());
            }
        } catch (Exception e) {
            System.out.println("异常: " + e.getMessage());
        }
    }

    @Test
    void demonstrateOptimization() {
        System.out.println("=== 优化效果演示 ===");
        System.out.println("优化前问题:");
        System.out.println("1. 大量IN查询导致临时表产生");
        System.out.println("2. 先查询bondUniCodes，再用于主查询，产生两次查询");
        System.out.println("3. 跨市场去重逻辑分离，无法利用索引优化");
        
        System.out.println("\n优化后改进:");
        System.out.println("1. 使用INNER JOIN替代大量IN查询");
        System.out.println("2. 子查询与主查询合并，减少查询次数");
        System.out.println("3. ROW_NUMBER()窗口函数直接在子查询中完成去重");
        System.out.println("4. 保持原有的WHERE条件和ORDER BY逻辑");
        
        System.out.println("\n预期性能提升:");
        System.out.println("- 减少临时表产生，降低内存使用");
        System.out.println("- 减少查询次数，提高响应速度");
        System.out.println("- 更好的索引利用，提高查询效率");
        System.out.println("- 在大数据量情况下效果更明显");
    }
}
