package com.innodealing.onshore.bondservice.mapper.bond;

import com.github.wz2cool.dynamic.mybatis.mapper.DynamicQueryMapper;
import com.github.wz2cool.dynamic.model.NormPagingResult;
import com.innodealing.onshore.bondservice.model.bo.BondBalanceStatisticsBO;
import com.innodealing.onshore.bondservice.model.dto.request.BondDetailInfoPageRequestDTO;
import com.innodealing.onshore.bondservice.model.entity.bond.OnshoreBondFilterDO;
import com.innodealing.onshore.bondservice.model.entity.bond.view.OnshoreBondFilterView;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;

/**
 * 基础筛选mapper
 *
 * <AUTHOR>
 */
public interface OnshoreBondFilterMapper extends DynamicQueryMapper<OnshoreBondFilterDO> {


    /**
     * 获取跨市场债券code
     *
     * @param comUniCodes 主体code
     * @param secondMarkets 二级市场
     * @return {@link List }<{@link Long }>
     */
    List<Long> listCrossMarketDedupStatusBondUniCodes(@Param("comUniCodes") Collection<Long> comUniCodes,
                                                      @Param("bondUniCodes") Collection<Long> bondUniCodes,
                                                      @Param("secondMarkets") Collection<Integer> secondMarkets);


    /**
     * 获取跨市场债券统计信息
     *
     * @return {@link OnshoreBondFilterView}
     */
    OnshoreBondFilterView selectOnshoreBondFilterStatistics(BondBalanceStatisticsBO req);

    /**
     * 获取债券筛选分页信息 - 优化版本
     * 使用INNER JOIN替代大量IN查询，避免临时表产生
     *
     * @param req 债券筛选分页请求
     * @return {@link NormPagingResult }<{@link OnshoreBondFilterDO }>
     */
    List<OnshoreBondFilterDO> selectPageOnshoreBondFilterPageOptimized(BondDetailInfoPageRequestDTO req,
                                                                       @Param("pageSize") int pageSize,
                                                                       @Param("offset") int offset);

    /**
     * 获取债券筛选分页信息 - 优化版本
     * 使用INNER JOIN替代大量IN查询，避免临时表产生
     *
     * @param req 债券筛选分页请求
     * @return {@link NormPagingResult }<{@link OnshoreBondFilterDO }>
     */
    int selectCountOnshoreBondFilterPageOptimized(BondDetailInfoPageRequestDTO req);
}
